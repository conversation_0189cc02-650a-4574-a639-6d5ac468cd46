import streamlit as st
import sqlite3
import pandas as pd
from datetime import datetime

DB_PATH = "reviews.db"

# --- Database Helpers ---
def get_connection():
    return sqlite3.connect(DB_PATH, check_same_thread=False)

def fetch_all_reviews():
    conn = get_connection()
    df = pd.read_sql_query("SELECT * FROM agent_review_replies", conn)
    conn.close()
    return df

def fetch_pending_reviews():
    conn = get_connection()
    df = pd.read_sql_query("SELECT * FROM agent_review_replies WHERE status = 'Pending'", conn)
    conn.close()
    return df

def update_review(review_id, status, modified_reply, sentiment, category):
    conn = get_connection()
    cursor = conn.cursor()
    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    cursor.execute("""
        UPDATE agent_review_replies 
        SET status = ?, modified_reply = ?, updated_at = ?, sentiment = ?, category = ?
        WHERE review_id = ?
    """, (status, modified_reply, now, sentiment, category, review_id))
    conn.commit()
    conn.close()

# --- Sidebar Stats ---
def show_statistics(df):
    total = len(df)
    pending = len(df[df['status'] == 'Pending'])
    accepted = len(df[df['status'] == 'Accepted'])
    rejected = len(df[df['status'] == 'Rejected'])
    edited = len(df[df['status'] == 'Edited'])

    st.sidebar.title("Moderation Summary")
    st.sidebar.markdown("---")

    st.sidebar.markdown("#### Overall")
    st.sidebar.metric("Total Reviews", total)

    st.sidebar.markdown("#### Status Breakdown")

    col1, col2 = st.sidebar.columns(2)
    with col1:
        st.success(f"Accepted:\n{accepted}")
        st.info(f"Pending:\n{pending}")
    with col2:
        st.error(f"Rejected:\n{rejected}")
        st.warning(f"Edited:\n{edited}")

    st.sidebar.markdown("---")
    st.sidebar.caption("Updated in real-time with each action.")


# --- Streamlit UI ---
st.set_page_config(page_title="Review Moderation", layout="wide")
st.title("Review Moderation Dashboard")
st.markdown("Manage, edit, and categorize agent replies to customer feedback using this moderation panel.")

st.divider()

all_reviews = fetch_all_reviews()
show_statistics(all_reviews)

pending_reviews = all_reviews[all_reviews["status"] == "Pending"]

if pending_reviews.empty:
    st.info("✅ No pending reviews. All replies have been processed.")
else:
    st.subheader("Pending Reviews")

    for _, row in pending_reviews.iterrows():
        with st.container():
            with st.expander(f"Review ID: {row['review_id']} — View & Moderate", expanded=False):

                st.markdown(f"**Customer Review:**")
                st.code(row['review_text'], language="text")

                st.markdown(f"**Agent Reply:**")
                st.code(row['agent_reply'], language="text")

                st.markdown("### Edit & Categorize")

                with st.form(key=f"form_{row['review_id']}", clear_on_submit=False):
                    edited_reply = st.text_area(
                        "Edited Reply",
                        value=row['agent_reply'],
                        key=f"edit_reply_{row['review_id']}"
                    )

                    col1, col2 = st.columns(2)
                    with col1:
                        sentiment_options = ["Positive", "Negative", "Neutral"]
                        try:
                            sentiment_index = sentiment_options.index(row['sentiment'])
                        except ValueError:
                            sentiment_index = 2  # Default to "Neutral" if not found

                        sentiment = st.selectbox(
                            "Sentiment",
                            options=sentiment_options,
                            index=sentiment_index,
                            key=f"sentiment_{row['review_id']}"
                        )
                    with col2:
                        category_options = ["Delivery", "Product", "Order Issues", "Service", "App Issues"]
                        try:
                            category_index = category_options.index(row['category'])
                        except ValueError:
                            category_index = 0  # Default to first option if not found

                        category = st.selectbox(
                            "Category",
                            options=category_options,
                            index=category_index,
                            key=f"category_{row['review_id']}"
                        )

                    st.markdown("#### Actions")

                    colA, colB, colC = st.columns([1, 1, 2])
                    submitted = False

                    with colA:
                        if st.form_submit_button("Accept", use_container_width=True):
                            update_review(row['review_id'], "Accepted", edited_reply, sentiment, category)
                            st.success("Reply accepted.")
                            submitted = True

                    with colB:
                        if st.form_submit_button("Reject", use_container_width=True):
                            update_review(row['review_id'], "Rejected", edited_reply, sentiment, category)
                            st.warning("Reply rejected.")
                            submitted = True

                    with colC:
                        if st.form_submit_button("Save Changes", use_container_width=True):
                            update_review(row['review_id'], "Edited", edited_reply, sentiment, category)
                            st.info("Changes saved.")
                            submitted = True

                if submitted:
                    st.rerun()




# python -m streamlit run app.py
