import sqlite3

# Connect to (or create) database
conn = sqlite3.connect('reviews.db')
cursor = conn.cursor()

# Drop the table if it exists (optional clean start)
cursor.execute("DROP TABLE IF EXISTS agent_review_replies")

# Create table with new columns
cursor.execute("""
CREATE TABLE IF NOT EXISTS agent_review_replies (
    review_id INTEGER PRIMARY KEY AUTOINCREMENT,
    review_text TEXT,
    agent_reply TEXT,
    modified_reply TEXT,
    status TEXT DEFAULT 'Pending',
    updated_at TEXT,
    sentiment TEXT,
    category TEXT
)
""")

# Sample data (all status = Pending)
sample_data = [
    ("The delivery was 40 minutes late.", 
     "We're sorry your order arrived late.", 
     None, 
     "Pending", 
     None, 
     "Negative", 
     "A"),

    ("Pizza was delicious and fresh!", 
     "Thanks for your kind words! Glad you enjoyed it.", 
     None, 
     "Pending", 
     None, 
     "Positive", 
     "B"),

    ("My order was incorrect.", 
     "We apologize for the mistake. We’ll do better next time.", 
     None, 
     "Pending", 
     None, 
     "Negative", 
     "C"),

    ("Staff was very polite and helpful.", 
     "Thank you for appreciating our team!", 
     None, 
     "Pending", 
     None, 
     "Positive", 
     "D"),

    ("The app kept crashing.", 
     "We’re working on fixing the issue. Sorry for the trouble.", 
     None, 
     "Pending", 
     None, 
     "Negative", 
     "A"),
]

cursor.executemany("""
INSERT INTO agent_review_replies 
(review_text, agent_reply, modified_reply, status, updated_at, sentiment, category)
VALUES (?, ?, ?, ?, ?, ?, ?)
""", sample_data)

conn.commit()
conn.close()

